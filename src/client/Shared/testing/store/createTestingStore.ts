import {configureStore} from "@reduxjs/toolkit";
import {deepMerge} from "@/src/client/Shared/helpers/DeepMerge/deepMerge";

import {LocationService} from "@/src/client/Shared/services/Location/LocationService";
import {DeckDraftService} from "@/src/client/DeckBuilding/application/ports/DeckDraftService";
import {FakeLocationService} from "@/src/client/Shared/services/Location/FakeLocationService";
import {FakeDeckDraftService} from "@/src/client/DeckBuilding/specs/helpers/fakes/FakeDeckDraftService";
import {subscribeToDeckDraft} from "@/src/client/DeckBuilding/application/subscribers/subscribeToDeckDraft";
import {setDeckDraftService} from "@/src/client/Shared/store/reduxStore/reduxStore";
import {RootState} from "@/src/client/Shared/store/appStore/rootState";
import {rootReducers} from "@/src/client/Shared/store/appStore/rootReducers";
import {rootInitialState} from "@/src/client/Shared/store/appStore/rootInitialState";

type OverrideState = Partial<{
  [K in keyof RootState]: Partial<RootState[K]>
}>;

export const createTestingStore = (
  overrides: OverrideState = {},
  extra?: { locationService?: LocationService; deckDraftService?: DeckDraftService; withDraftSubscriber?: boolean }
) => {
  const deckDraft = extra?.deckDraftService ?? new FakeDeckDraftService();
  setDeckDraftService(deckDraft);
  const store = configureStore({
    reducer: rootReducers,
    preloadedState: Object.fromEntries(
      Object.entries(rootInitialState).map(([key, initial]) => {
        const override = overrides[key as keyof RootState];
        const merged = override ? deepMerge(initial, override) : initial;
        return [key, merged];
      })
    ) as unknown as RootState,
    middleware: (getDefaultMiddleware) => {
      return getDefaultMiddleware({
        thunk: {
          extraArgument: {
            locationService: extra?.locationService ?? new FakeLocationService(),
            deckDraftService: deckDraft,
          },
        },
      });
    },
  });
  if (extra?.withDraftSubscriber) {
    subscribeToDeckDraft(store, deckDraft);
  }
  return store;
}
