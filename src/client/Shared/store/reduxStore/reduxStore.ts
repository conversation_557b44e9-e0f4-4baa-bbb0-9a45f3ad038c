import {configureStore} from "@reduxjs/toolkit";
import {DeckDraftService} from "@/src/client/DeckBuilding/application/ports/DeckDraftService";
import {RealLocationService} from "@/src/client/Shared/services/Location/RealLocationService";
import {BrowserDeckDraftService} from "@/src/client/DeckBuilding/infrastructure/services/deckDraft/browserDeckDraftService";
import {subscribeToDeckDraft} from "@/src/client/DeckBuilding/application/subscribers/subscribeToDeckDraft";

import {rootReducers} from "@/src/client/Shared/store/appStore/rootReducers";

export const locationService = new RealLocationService();
export let deckDraftService: DeckDraftService = new BrowserDeckDraftService();
export const setDeckDraftService = (service: DeckDraftService) => {
  deckDraftService = service;
};

export const reduxStore = configureStore({
  reducer: rootReducers,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      thunk: {extraArgument: {locationService, deckDraftService}},
      serializableCheck: {
        ignoredActions: ['catalog/cardsLoaded'],
      },
    }),
});

subscribeToDeckDraft(reduxStore, deckDraftService);
