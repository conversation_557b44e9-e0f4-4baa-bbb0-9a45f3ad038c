import { useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useMutation } from 'convex/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { RootState } from '@/src/client/Shared/store/appStore/rootState';
import { AppDispatch } from '@/src/client/Shared/store/appStore/appDispatch';
import { deckCreatedEvent } from '@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents';
import { useGameId } from '@/src/client/Shared/hooks/useGameId/useGameId';
import { useLocale } from '@/src/client/Shared/hooks/useLocale/useLocale';
import { buildEditDeckUrlWithQuery } from '@/src/client/Shared/helpers/UrlBuilder/urlBuilder';

export const useDeckCreation = () => {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const searchParams = useSearchParams();
  const locale = useLocale();
  const { cardsInDeck, name, deckId } = useSelector((state: RootState) => state.deckBuilder);
  const gameId = useGameId();
  const saveDeck = useMutation(api.mutations.saveDeck.endpoint);
  
  const isCreatingRef = useRef(false);

  const generateDefaultName = useCallback(() => {
    return `deck-${crypto.randomUUID()}`;
  }, []);

  const createDeckIfNeeded = useCallback(async (forceName?: string) => {
    // Prevent multiple simultaneous creations
    if (isCreatingRef.current || deckId) return null;

    const cards = Object.values(cardsInDeck).map(({ card, quantity }) => ({
      cardId: card.id,
      quantity,
    }));

    // Only create if there are cards
    if (cards.length === 0) return null;

    try {
      isCreatingRef.current = true;
      
      const effectiveName = forceName || name || generateDefaultName();
      
      const newDeckId = await saveDeck({
        gameId: gameId as Id<'games'>,
        name: effectiveName,
        cards,
      });

      // Dispatch the event
      dispatch(deckCreatedEvent({ deckId: newDeckId as string, name: effectiveName }));

      // Update URL to include the new deck ID
      const search = new URLSearchParams(searchParams.toString());
      search.delete('deckId');
      search.delete('locale');
      const query = search.toString();
      
      const newUrl = buildEditDeckUrlWithQuery(
        locale,
        gameId,
        newDeckId as string,
        query ? `?${query}` : ''
      );
      
      router.replace(newUrl);

      return newDeckId as string;
    } catch (error) {
      console.error('Failed to create deck:', error);
      return null;
    } finally {
      isCreatingRef.current = false;
    }
  }, [cardsInDeck, name, deckId, gameId, saveDeck, dispatch, generateDefaultName, searchParams, locale, router]);

  return {
    createDeckIfNeeded,
    isCreating: isCreatingRef.current,
  };
};
