import { useState, useEffect, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { RootState } from '@/src/client/Shared/store/appStore/rootState';
import { AppDispatch } from '@/src/client/Shared/store/appStore/appDispatch';
import { deckCreatedEvent } from '@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents';
import { useGameId } from '@/src/client/Shared/hooks/useGameId/useGameId';
import { useDeckId } from '@/src/client/DeckBuilding/infrastructure/hooks/useDeckId/useDeckId';

export const useDeckName = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { name: deckName, deckId: statedeckId, cardsInDeck } = useSelector((state: RootState) => state.deckBuilder);
  const gameId = useGameId();
  const urlDeckId = useDeckId();
  const updateDeck = useMutation(api.mutations.updateDeck.endpoint);
  const saveDeck = useMutation(api.mutations.saveDeck.endpoint);
  
  const [localName, setLocalName] = useState(deckName || '');
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInitialMount = useRef(true);

  // Sync local name with deck name from state
  useEffect(() => {
    if (deckName !== null) {
      setLocalName(deckName);
    }
  }, [deckName]);

  const saveNameChange = useCallback(async (newName: string) => {
    if (!newName.trim()) return;

    const cards = Object.values(cardsInDeck).map(({ card, quantity }) => ({
      cardId: card.id,
      quantity,
    }));

    try {
      const effectiveDeckId = statedeckId || urlDeckId;
      
      if (effectiveDeckId) {
        await updateDeck({
          deckId: effectiveDeckId as Id<'decks'>,
          name: newName.trim(),
          cards,
        });
      } else if (cards.length > 0) {
        // Only create a new deck if there are cards
        const newDeckId = await saveDeck({
          gameId: gameId as Id<'games'>,
          name: newName.trim(),
          cards,
        });
        dispatch(deckCreatedEvent({ deckId: newDeckId as string, name: newName.trim() }));
      }
    } catch (error) {
      console.error('Failed to save deck name:', error);
    }
  }, [cardsInDeck, statedeckId, urlDeckId, gameId, updateDeck, saveDeck, dispatch]);

  const handleNameChange = useCallback((newName: string) => {
    setLocalName(newName);

    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set new timeout for debounced save
    timeoutRef.current = setTimeout(() => {
      saveNameChange(newName);
    }, 1000);
  }, [saveNameChange]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    name: localName,
    setName: handleNameChange,
  };
};
