import { useEffect, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useMutation } from 'convex/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { RootState } from '@/src/client/Shared/store/appStore/rootState';
import { AppDispatch } from '@/src/client/Shared/store/appStore/appDispatch';
import { deckCreatedEvent } from '@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents';
import { useGameId } from '@/src/client/Shared/hooks/useGameId/useGameId';
import { useLocale } from '@/src/client/Shared/hooks/useLocale/useLocale';
import { buildEditDeckUrlWithQuery } from '@/src/client/Shared/helpers/UrlBuilder/urlBuilder';

export const useAutoSaveDeck = () => {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const searchParams = useSearchParams();
  const locale = useLocale();
  const { cardsInDeck, name, deckId } = useSelector((state: RootState) => state.deckBuilder);
  const gameId = useGameId();
  const saveDeck = useMutation(api.mutations.saveDeck.endpoint);
  const updateDeck = useMutation(api.mutations.updateDeck.endpoint);

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInitialMount = useRef(true);
  const isCreatingRef = useRef(false);

  const saveAutomatically = useCallback(async () => {
    const cards = Object.values(cardsInDeck).map(({ card, quantity }) => ({
      cardId: card.id,
      quantity,
    }));

    if (cards.length === 0) return;

    // Prevent multiple simultaneous creations
    if (!deckId && isCreatingRef.current) return;

    try {
      if (deckId) {
        // Update existing deck
        await updateDeck({
          deckId: deckId as Id<'decks'>,
          name: name || `deck-${crypto.randomUUID()}`,
          cards,
        });
      } else {
        // Create new deck - ONLY point of creation
        isCreatingRef.current = true;

        const effectiveName = name || `deck-${crypto.randomUUID()}`;

        const newDeckId = await saveDeck({
          gameId: gameId as Id<'games'>,
          name: effectiveName,
          cards,
        });

        // Dispatch the event
        dispatch(deckCreatedEvent({ deckId: newDeckId as string, name: effectiveName }));

        // Update URL to include the new deck ID
        const search = new URLSearchParams(searchParams.toString());
        search.delete('deckId');
        search.delete('locale');
        const query = search.toString();

        const newUrl = buildEditDeckUrlWithQuery(
          locale,
          gameId,
          newDeckId as string,
          query ? `?${query}` : ''
        );

        router.replace(newUrl);

        isCreatingRef.current = false;
      }
    } catch (error) {
      console.error('Failed to auto-save deck:', error);
      isCreatingRef.current = false;
    }
  }, [cardsInDeck, name, deckId, gameId, saveDeck, updateDeck, dispatch, searchParams, locale, router]);

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      saveAutomatically();
    }, 1000);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [saveAutomatically]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
};
