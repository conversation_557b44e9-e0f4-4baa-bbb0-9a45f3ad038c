import { useEffect, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { RootState } from '@/src/client/Shared/store/appStore/rootState';
import { AppDispatch } from '@/src/client/Shared/store/appStore/appDispatch';
import { deckCreatedEvent } from '@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents';
import { useGameId } from '@/src/client/Shared/hooks/useGameId/useGameId';

export const useAutoSaveDeck = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { cardsInDeck, name, deckId } = useSelector((state: RootState) => state.deckBuilder);
  const gameId = useGameId();
  const saveDeck = useMutation(api.mutations.saveDeck.endpoint);
  const updateDeck = useMutation(api.mutations.updateDeck.endpoint);
  
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInitialMount = useRef(true);

  const generateDefaultName = useCallback(() => {
    return `deck-${crypto.randomUUID()}`;
  }, []);

  const saveAutomatically = useCallback(async () => {
    const cards = Object.values(cardsInDeck).map(({ card, quantity }) => ({
      cardId: card.id,
      quantity,
    }));

    if (cards.length === 0) return;

    try {
      const effectiveName = name || generateDefaultName();

      if (deckId) {
        await updateDeck({
          deckId: deckId as Id<'decks'>,
          name: effectiveName,
          cards,
        });
      } else {
        const newDeckId = await saveDeck({
          gameId: gameId as Id<'games'>,
          name: effectiveName,
          cards,
        });
        dispatch(deckCreatedEvent({ deckId: newDeckId as string, name: effectiveName }));
      }
    } catch (error) {
      console.error('Failed to auto-save deck:', error);
    }
  }, [cardsInDeck, name, deckId, gameId, saveDeck, updateDeck, dispatch, generateDefaultName]);

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      saveAutomatically();
    }, 1000);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [saveAutomatically]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
};
