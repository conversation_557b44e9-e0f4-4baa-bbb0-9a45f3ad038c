import { useEffect, useCallback, useRef } from 'react';
import { useSelector } from 'react-redux';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { RootState } from '@/src/client/Shared/store/appStore/rootState';
import { useDeckCreation } from '@/src/client/DeckBuilding/infrastructure/hooks/useDeckCreation/useDeckCreation';

export const useAutoSaveDeck = () => {
  const { cardsInDeck, name, deckId } = useSelector((state: RootState) => state.deckBuilder);
  const updateDeck = useMutation(api.mutations.updateDeck.endpoint);
  const { createDeckIfNeeded } = useDeckCreation();

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInitialMount = useRef(true);

  const saveAutomatically = useCallback(async () => {
    const cards = Object.values(cardsInDeck).map(({ card, quantity }) => ({
      cardId: card.id,
      quantity,
    }));

    if (cards.length === 0) return;

    try {
      if (deckId) {
        // Update existing deck
        await updateDeck({
          deckId: deckId as Id<'decks'>,
          name: name || `deck-${crypto.randomUUID()}`,
          cards,
        });
      } else {
        // Create new deck using centralized logic
        await createDeckIfNeeded();
      }
    } catch (error) {
      console.error('Failed to auto-save deck:', error);
    }
  }, [cardsInDeck, name, deckId, updateDeck, createDeckIfNeeded]);

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      saveAutomatically();
    }, 1000);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [saveAutomatically]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
};
