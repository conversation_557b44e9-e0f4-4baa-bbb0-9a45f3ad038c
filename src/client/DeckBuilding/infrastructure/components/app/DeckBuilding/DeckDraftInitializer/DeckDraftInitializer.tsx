'use client';

import {useEffect, useRef} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {loadDeckDraft} from '@/src/client/DeckBuilding/application/commands/loadDeckDraft/loadDeckDraft';
import {isCatalogLoading} from '@/src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading';
import {RootState} from "@/src/client/Shared/store/appStore/rootState";
import {AppDispatch} from "@/src/client/Shared/store/appStore/appDispatch";

export const DeckDraftInitializer = () => {
  const dispatch = useDispatch<AppDispatch>();
  const initialized = useRef(false);
  const catalogLoaded = useSelector((state: RootState) => !isCatalogLoading(state));
  const deckLoaded = useSelector(
    (state: RootState) => Object.keys(state.deckBuilder.cardsInDeck).length > 0,
  );

  useEffect(() => {
    if (initialized.current) return;
    if (!catalogLoaded) return;
    if (deckLoaded) return;
    dispatch(loadDeckDraft());
    initialized.current = true;
  }, [dispatch, catalogLoaded, deckLoaded]);

  return null;
};
