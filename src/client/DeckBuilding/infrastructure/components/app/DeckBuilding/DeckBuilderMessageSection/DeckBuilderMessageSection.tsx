'use client';

import {useDispatch, useSelector} from 'react-redux';
import {getDeckBuilderMessageStatus} from '@/src/client/DeckBuilding/application/queries/getDeckBuilderMessageStatus/getDeckBuilderMessageStatus';
import {deckBuilderIdleEvent} from '@/src/client/DeckBuilding/domain/DeckBuilder/ui/deckBuilderUiEvents';
import DeckBuilderCreationOnboardingMessage from './DeckBuilderCreationOnboardingMessage';
import DeckBuilderDeckLoadingMessage from './DeckBuilderDeckLoadingMessage';
import DeckBuilderDeckSavingMessage from './DeckBuilderDeckSavingMessage';
import DeckBuilderDeckSavedMessage from './DeckBuilderDeckSavedMessage';
import DeckBuilderShouldBeSavedMessage from './DeckBuilderShouldBeSavedMessage';
import {useEffect} from 'react';

const DeckBuilderMessageSection = () => {
  const dispatch = useDispatch();
  const status = useSelector(getDeckBuilderMessageStatus);

  useEffect(() => {
    if (status === 'saved') {
      const id = setTimeout(() => dispatch(deckBuilderIdleEvent()), 3000);
      return () => clearTimeout(id);
    }
    return undefined;
  }, [status, dispatch]);

  if (status === 'loading') return <DeckBuilderDeckLoadingMessage/>;
  if (status === 'saving') return <DeckBuilderDeckSavingMessage/>;
  if (status === 'saved') return <DeckBuilderDeckSavedMessage/>;
  if (status === 'onboarding' || status === 'idle') return <DeckBuilderCreationOnboardingMessage/>;
  if (status === 'shouldBeSaved') return <DeckBuilderShouldBeSavedMessage/>;

  return null;
};

export default DeckBuilderMessageSection;
