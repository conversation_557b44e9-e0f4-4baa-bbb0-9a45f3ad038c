import { render } from '@testing-library/react'
import { Provider } from 'react-redux'
import { createTestingStore } from '@/src/client/Shared/testing/store/createTestingStore'
import { DeckDraftInitializer } from '@/src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckDraftInitializer/DeckDraftInitializer'
import { PINOCCHIO_STRINGS_ATTACHED } from '@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards'
import { FakeDeckDraftService } from '@/src/client/DeckBuilding/specs/helpers/fakes/FakeDeckDraftService'

describe('DeckDraftInitializer', () => {
  describe('When the deck builder already has a deck', () => {
    it('should not load the draft', () => {
      // Arrange
      const deckDraftService = new FakeDeckDraftService({
        name: 'draft',
        cards: [{ cardId: PINOCCHIO_STRINGS_ATTACHED.id, quantity: 2 }],
      })
      const store = createTestingStore({
        catalog: { status: 'success', cards: { [PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED } },
        deckBuilder: {
          cardsInDeck: { [PINOCCHIO_STRINGS_ATTACHED.id]: { card: PINOCCHIO_STRINGS_ATTACHED, quantity: 1 } },
        },
      }, { deckDraftService })

      // Act
      render(<Provider store={store}><DeckDraftInitializer /></Provider>)

      // Assert
      expect(store.getState().deckBuilder.cardsInDraftDeck).toEqual({})
    })
  })

  describe('When the deck builder is empty', () => {
    it('should load the saved draft', () => {
      // Arrange
      const deckDraftService = new FakeDeckDraftService({
        name: 'draft',
        cards: [{ cardId: PINOCCHIO_STRINGS_ATTACHED.id, quantity: 2 }],
      })
      const store = createTestingStore({
        catalog: { status: 'success', cards: { [PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED } },
        deckBuilder: {},
      }, { deckDraftService })

      // Act
      render(<Provider store={store}><DeckDraftInitializer /></Provider>)

      // Assert
      expect(store.getState().deckBuilder.cardsInDraftDeck).toEqual({
        [PINOCCHIO_STRINGS_ATTACHED.id]: { card: PINOCCHIO_STRINGS_ATTACHED, quantity: 2 },
      })
    })
  })
})
