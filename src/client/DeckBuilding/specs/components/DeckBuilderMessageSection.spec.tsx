import {render, screen} from '@testing-library/react';
import {Provider} from 'react-redux';
import DeckBuilderMessageSection from '@/src/client/DeckBuilding/infrastructure/components/app/DeckBuilding/DeckBuilderMessageSection/DeckBuilderMessageSection';
import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {PINOCCHIO_STRINGS_ATTACHED} from '@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards';

describe('DeckBuilderMessageSection', () => {
  describe('When a draft exists', () => {
    it('should prompt to save the deck', () => {
      // Arrange
      const store = createTestingStore({
        catalog: {status: 'success'},
        gameSettings: {status: 'success'},
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
          cardsInDraftDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 2,
            },
          },
        },
      });

      // Act
      render(
        <Provider store={store}>
          <DeckBuilderMessageSection />
        </Provider>
      );

      // Assert
      expect(
        screen.getByText("Your deck content changed, don't forget to save it!")
      ).toBeTruthy();
    });
  });

  describe('When the draft matches the current deck', () => {
    it('should display the onboarding message', () => {
      // Arrange
      const store = createTestingStore({
        catalog: {status: 'success'},
        gameSettings: {status: 'success'},
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
          cardsInDraftDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
        },
      });

      // Act
      render(
        <Provider store={store}>
          <DeckBuilderMessageSection />
        </Provider>
      );

      // Assert
      expect(
        screen.getByText(
          /Click on the plus button at the bottom right of each card to start building your deck./
        )
      ).toBeTruthy();
      expect(
        screen.queryByText("Your deck content changed, don't forget to save it!")
      ).toBeNull();
    });
  });

  describe('When the deck was saved', () => {
    it('should display a confirmation message', () => {
      // Arrange
      const store = createTestingStore({
        catalog: {status: 'success'},
        gameSettings: {status: 'success'},
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
          cardsInDraftDeck: {},
        },
        deckBuilderUi: {status: 'saved'},
      });

      // Act
      render(
        <Provider store={store}>
          <DeckBuilderMessageSection />
        </Provider>
      );

      // Assert
      expect(screen.getByText('Your deck has been saved!')).toBeTruthy();
      expect(
        screen.queryByText("Your deck content changed, don't forget to save it!")
      ).toBeNull();
    });
  });

  describe('When no draft exists and the builder is idle', () => {
    it('should display the onboarding message', () => {
      // Arrange
      const store = createTestingStore({
        catalog: {status: 'success'},
        gameSettings: {status: 'success'},
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
          cardsInDraftDeck: {},
        },
        deckBuilderUi: {status: 'idle'},
      });

      // Act
      render(
        <Provider store={store}>
          <DeckBuilderMessageSection />
        </Provider>
      );

      // Assert
      expect(
        screen.getByText(
          /Click on the plus button at the bottom right of each card to start building your deck./
        )
      ).toBeTruthy();
      expect(
        screen.queryByText("Your deck content changed, don't forget to save it!")
      ).toBeNull();
    });
  });

  describe('When no draft exists and the deck is empty', () => {
    it('should display the onboarding message', () => {
      // Arrange
      const store = createTestingStore({
        catalog: {status: 'success'},
        gameSettings: {status: 'success'},
        deckBuilder: {},
      });

      // Act
      render(
        <Provider store={store}>
          <DeckBuilderMessageSection />
        </Provider>
      );

      // Assert
      expect(
        screen.getByText(
          /Click on the plus button at the bottom right of each card to start building your deck./
        )
      ).toBeTruthy();
      expect(
        screen.queryByText("Your deck content changed, don't forget to save it!")
      ).toBeNull();
    });
  });
});
