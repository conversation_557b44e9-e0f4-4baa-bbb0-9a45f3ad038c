import {createTestingStore} from '@/src/client/Shared/testing/store/createTestingStore';
import {PINOCCHIO_STRINGS_ATTACHED} from '@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards';
import {getDeckBuilderMessageStatus} from '@/src/client/DeckBuilding/application/queries/getDeckBuilderMessageStatus/getDeckBuilderMessageStatus';

describe('getDeckBuilderMessageStatus', () => {
  describe('When a draft exists', () => {
    it('should return shouldBeSaved', () => {
      // Arrange
      const {getState} = createTestingStore({
        catalog: {status: 'success'},
        gameSettings: {status: 'success'},
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
          cardsInDraftDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 2,
            },
          },
        },
      });

      // Act
      const status = getDeckBuilderMessageStatus(getState());

      // Assert
      expect(status).toBe('shouldBeSaved');
    });
  });

  describe('When the draft matches the current deck', () => {
    it('should return onboarding', () => {
      // Arrange
      const {getState} = createTestingStore({
        catalog: {status: 'success'},
        gameSettings: {status: 'success'},
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
          cardsInDraftDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
        },
      });

      // Act
      const status = getDeckBuilderMessageStatus(getState());

      // Assert
      expect(status).toBe('onboarding');
    });
  });

  describe('When the deck was saved', () => {
    it('should return saved', () => {
      // Arrange
      const {getState} = createTestingStore({
        catalog: {status: 'success'},
        gameSettings: {status: 'success'},
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
          cardsInDraftDeck: {},
        },
        deckBuilderUi: {status: 'saved'},
      });

      // Act
      const status = getDeckBuilderMessageStatus(getState());

      // Assert
      expect(status).toBe('saved');
    });
  });

  describe('When no draft exists and the builder is idle', () => {
    it('should return onboarding', () => {
      // Arrange
      const {getState} = createTestingStore({
        catalog: {status: 'success'},
        gameSettings: {status: 'success'},
        deckBuilder: {
          cardsInDeck: {
            [PINOCCHIO_STRINGS_ATTACHED.id]: {
              card: PINOCCHIO_STRINGS_ATTACHED,
              quantity: 1,
            },
          },
          cardsInDraftDeck: {},
        },
        deckBuilderUi: {status: 'idle'},
      });

      // Act
      const status = getDeckBuilderMessageStatus(getState());

      // Assert
      expect(status).toBe('onboarding');
    });
  });

  describe('When no draft exists and the deck is empty', () => {
    it('should return onboarding', () => {
      // Arrange
      const {getState} = createTestingStore({
        catalog: {status: 'success'},
        gameSettings: {status: 'success'},
        deckBuilder: {},
      });

      // Act
      const status = getDeckBuilderMessageStatus(getState());

      // Assert
      expect(status).toBe('onboarding');
    });
  });
});
