import {addCardToDeck} from "@/src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeck";
import {createTestingStore} from "@/src/client/Shared/testing/store/createTestingStore";
import {PINOCCHIO_STRINGS_ATTACHED} from "@/src/client/DeckBuilding/specs/helpers/factories/fakeCatalogCards";

import {getTotalCardsInDeck} from "@/src/client/DeckBuilding/application/queries/getTotalCardsInDeck/getTotalCardsInDeck";
import {findDeckCardById} from "@/src/client/DeckBuilding/application/queries/findDeckCardById/findDeckCardById";

describe('AddCardToDeck', () => {
  describe('When a card is added to the deck', () => {
    describe('When the card is not in the deck', () => {
      it('should add the card to the deck with a quantity of 1', async () => {
        // Arrange
        const {dispatch, getState} = createTestingStore({
          catalog: {
            cards: {
              [PINOCCHIO_STRINGS_ATTACHED.id]: PINOCCHIO_STRINGS_ATTACHED,
            },
          }
        });

        // Act
        await dispatch(addCardToDeck({cardId: PINOCCHIO_STRINGS_ATTACHED.id}));

        // Assert
        expect(getTotalCardsInDeck(getState())).toBe(1);
        expect(findDeckCardById(getState(), PINOCCHIO_STRINGS_ATTACHED.id).quantity).toBe(1);
        expect(getState().deckBuilder.cardsInDraftDeck[PINOCCHIO_STRINGS_ATTACHED.id].quantity).toBe(1);
      });
    });

    describe('When the card is already in the deck', () => {
      describe('When the max quantity is reached', () => {
        it('should not add the card to the deck', async () => {
          // Arrange
          const {dispatch, getState} = createTestingStore({
            deckBuilder: {
              cardsInDraftDeck: {
                [PINOCCHIO_STRINGS_ATTACHED.id]: {
                  card: PINOCCHIO_STRINGS_ATTACHED,
                  quantity: 4,
                },
              },
            },
            catalog: {
              cards: {
                [PINOCCHIO_STRINGS_ATTACHED.id]: {
                  ...PINOCCHIO_STRINGS_ATTACHED,
                  maxDeckQuantity: 4,
                },
              },
            }
          });

          // Act
          await dispatch(addCardToDeck({cardId: PINOCCHIO_STRINGS_ATTACHED.id}));

          // Assert
          expect(getTotalCardsInDeck(getState())).toBe(4);
          expect(findDeckCardById(getState(), PINOCCHIO_STRINGS_ATTACHED.id).quantity).toBe(4);
          expect(getState().deckBuilder.cardsInDraftDeck[PINOCCHIO_STRINGS_ATTACHED.id].quantity).toBe(4);
        });
      });

      describe('When the max quantity is not reached', () => {
        it('should add the card to the deck', async () => {
          // Arrange
          const {dispatch, getState} = createTestingStore({
            deckBuilder: {
              cardsInDraftDeck: {
                [PINOCCHIO_STRINGS_ATTACHED.id]: {
                  card: PINOCCHIO_STRINGS_ATTACHED,
                  quantity: 3,
                },
              },
            },
            catalog: {
              cards: {
                [PINOCCHIO_STRINGS_ATTACHED.id]: {
                  ...PINOCCHIO_STRINGS_ATTACHED,
                  maxDeckQuantity: 4,
                },
              },
            }
          });

          // Act
          await dispatch(addCardToDeck({cardId: PINOCCHIO_STRINGS_ATTACHED.id}));

          // Assert
          expect(getTotalCardsInDeck(getState())).toBe(4);
          expect(findDeckCardById(getState(), PINOCCHIO_STRINGS_ATTACHED.id).quantity).toBe(4);
          expect(getState().deckBuilder.cardsInDraftDeck[PINOCCHIO_STRINGS_ATTACHED.id].quantity).toBe(4);
        });
      });
    });
  });
});