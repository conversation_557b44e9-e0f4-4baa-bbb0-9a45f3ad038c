import {createReducer} from '@reduxjs/toolkit'
import {DeckBuilderCard} from "@/src/client/DeckBuilding/domain/DeckBuilder/DeckBuilderCard";
import {
  cardAddedToDeckEvent,
  cardDetailsDisplayedEvent,
  cardDetailsHiddenEvent,
  cardQuantityDecreasedEvent,
  cardQuantityIncreasedEvent,
  cardRemovedFromDeckEvent,
  deckBuilderViewSwitchedEvent,
  deckLoadedEvent,
  deckDraftUpdatedEvent,
  deckDraftClearedEvent
} from "@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents";

export interface DeckBuilderState {
  cardsInDeck: Record<string, { card: DeckBuilderCard; quantity: number }>;
  cardsInDraftDeck: Record<string, { card: DeckBuilderCard; quantity: number }>;
  cardDetailsDisplayed: { cardImage: string; cardName: string } | null;
  view: 'catalog' | 'deck';
  name: string | null;
}

export const initialDeckBuilderState: DeckBuilderState = {
  cardsInDeck: {},
  cardsInDraftDeck: {},
  cardDetailsDisplayed: null,
  view: 'catalog',
  name: null,
};

export const deckBuilderReducer = createReducer(initialDeckBuilderState, (builder) => {
  builder
    .addCase(cardAddedToDeckEvent, (state, {payload}) => {
      state.cardsInDraftDeck[payload.card.id] = payload;
    })
    .addCase(cardQuantityIncreasedEvent, (state, {payload}) => {
      state.cardsInDraftDeck[payload.id].quantity = payload.newQuantity;
    })
    .addCase(cardQuantityDecreasedEvent, (state, {payload}) => {
      state.cardsInDraftDeck[payload.id].quantity = payload.newQuantity;
    })
    .addCase(cardRemovedFromDeckEvent, (state, {payload}) => {
      delete state.cardsInDraftDeck[payload];
    })
    .addCase(cardDetailsDisplayedEvent, (state, {payload}) => {
      state.cardDetailsDisplayed = payload;
    })
    .addCase(cardDetailsHiddenEvent, (state) => {
      state.cardDetailsDisplayed = null;
    })
    .addCase(deckBuilderViewSwitchedEvent, (state, {payload}) => {
      state.view = payload;
    })
    .addCase(deckLoadedEvent, (state, {payload}) => {
      state.cardsInDeck = {};
      state.name = payload.name;
      payload.cards.forEach(({card, quantity}) => {
        state.cardsInDeck[card.id] = {card, quantity};
      });
    })
    .addCase(deckDraftUpdatedEvent, (state, {payload}) => {
      state.cardsInDraftDeck = {};
      payload.cards.forEach(({card, quantity}) => {
        state.cardsInDraftDeck[card.id] = {card, quantity};
      });
    })
    .addCase(deckDraftClearedEvent, (state) => {
      state.cardsInDraftDeck = {};
    });
});