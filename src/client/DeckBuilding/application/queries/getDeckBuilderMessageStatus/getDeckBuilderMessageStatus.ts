import {createSelector} from "reselect";
import {isCatalogLoading} from "@/src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading";
import {isGameSettingsLoading} from "@/src/client/DeckBuilding/application/queries/isGameSettingsLoading/isGameSettingsLoading";
import {getDeckBuilderUiStatus} from "@/src/client/DeckBuilding/application/queries/getDeckBuilderUiStatus/getDeckBuilderUiStatus";
import {getTotalCardsInDeck} from "@/src/client/DeckBuilding/application/queries/getTotalCardsInDeck/getTotalCardsInDeck";
import {getCardsInDeckMap} from "@/src/client/DeckBuilding/application/queries/getCardsInDeckMap/getCardsInDeckMap";
import {getCardsInDraftDeckMap} from "@/src/client/DeckBuilding/application/queries/getCardsInDraftDeckMap/getCardsInDraftDeckMap";

export type DeckBuilderMessageStatus =
  | 'loading'
  | 'saving'
  | 'saved'
  | 'onboarding'
  | 'shouldBeSaved'
  | 'idle'
  | null;

export const getDeckBuilderMessageStatus = createSelector(
  isCatalogLoading,
  isGameSettingsLoading,
  getDeckBuilderUiStatus,
  getCardsInDeckMap,
  getCardsInDraftDeckMap,
  getTotalCardsInDeck,
  (
    catalogLoading,
    settingsLoading,
    uiStatus,
    deckCards,
    draftCards,
    totalCards,
  ): DeckBuilderMessageStatus => {
    if (catalogLoading || settingsLoading) return 'loading';
    if (uiStatus === 'saving') return 'saving';
    if (uiStatus === 'saved') return 'saved';

    const draftExists = Object.keys(draftCards).length > 0;
    const currentDeck = Object.values(deckCards).map(({card, quantity}) => ({cardId: card.id, quantity}));
    const currentDraft = Object.values(draftCards).map(({card, quantity}) => ({cardId: card.id, quantity}));
    const deckUnchanged = draftExists && JSON.stringify(currentDraft) === JSON.stringify(currentDeck);

    if (!draftExists || deckUnchanged || totalCards === 0) return 'onboarding';
    if (draftExists && totalCards > 0) return 'shouldBeSaved';
    return null;
  },
);
