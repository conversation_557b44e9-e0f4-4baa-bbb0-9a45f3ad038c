import {createSelector} from "reselect";
import {isCatalogLoading} from "@/src/client/DeckBuilding/application/queries/isCatalogLoading/isCatalogLoading";
import {isGameSettingsLoading} from "@/src/client/DeckBuilding/application/queries/isGameSettingsLoading/isGameSettingsLoading";
import {getDeckBuilderUiStatus} from "@/src/client/DeckBuilding/application/queries/getDeckBuilderUiStatus/getDeckBuilderUiStatus";
import {getTotalCardsInDeck} from "@/src/client/DeckBuilding/application/queries/getTotalCardsInDeck/getTotalCardsInDeck";

export type DeckBuilderMessageStatus =
  | 'loading'
  | 'saving'
  | 'saved'
  | 'onboarding'
  | 'shouldBeSaved'
  | 'idle'
  | null;

export const getDeckBuilderMessageStatus = createSelector(
  isCatalogLoading,
  isGameSettingsLoading,
  getDeckBuilderUiStatus,
  getTotalCardsInDeck,
  (
    catalogLoading,
    settingsLoading,
    uiStatus,
    totalCards,
  ): DeckBuilderMessageStatus => {
    if (catalogLoading || settingsLoading) return 'loading';
    if (uiStatus === 'saving') return 'saving';
    if (uiStatus === 'saved') return 'saved';

    if (totalCards === 0) return 'onboarding';
    return 'idle';
  },
);
