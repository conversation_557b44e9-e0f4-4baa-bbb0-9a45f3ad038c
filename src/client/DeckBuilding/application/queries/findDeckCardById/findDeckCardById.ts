import {RootState} from "@/src/client/Shared/store/appStore/rootState";
import {getCardsInDeckMap} from '../getCardsInDeckMap/getCardsInDeckMap';
import {getCardsInDraftDeckMap} from '../getCardsInDraftDeckMap/getCardsInDraftDeckMap';

export const findDeckCardById = (state: RootState, id: string) => {
  const draftCards = getCardsInDraftDeckMap(state);
  if (Object.keys(draftCards).length > 0) {
    return draftCards[id];
  }
  return getCardsInDeckMap(state)[id];
};
