import {createSelector} from "reselect";
import {getCardsInDeckMap} from "../getCardsInDeckMap/getCardsInDeckMap";
import {getCardsInDraftDeckMap} from "../getCardsInDraftDeckMap/getCardsInDraftDeckMap";

export const getCardsInDeck = createSelector(
  getCardsInDeckMap,
  getCardsInDraftDeckMap,
  (cardsInDeck, draftCards) => {
    const source = Object.keys(draftCards).length > 0 ? draftCards : cardsInDeck;
    return Object.values(source).map(({ card, quantity }) => ({
      ...card,
      quantity,
    }));
  }
);