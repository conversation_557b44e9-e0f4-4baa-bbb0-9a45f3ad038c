import {createSelector} from "reselect";
import {getCardsInDeckMap} from "../getCardsInDeckMap/getCardsInDeckMap";
import {getCardsInDraftDeckMap} from "../getCardsInDraftDeckMap/getCardsInDraftDeckMap";

export const getTotalCardsInDeck = createSelector(
  getCardsInDeckMap,
  getCardsInDraftDeckMap,
  (cardsInDeck, draftCards) => {
    const source = Object.keys(draftCards).length > 0 ? draftCards : cardsInDeck;
    return Object.values(source).reduce(
      (total, card) => total + card.quantity,
      0
    );
  }
);