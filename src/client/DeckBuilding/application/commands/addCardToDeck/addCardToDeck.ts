import {createAsyncThunk} from "@reduxjs/toolkit";

import {AddCardToDeckRequest} from "@/src/client/DeckBuilding/application/commands/addCardToDeck/addCardToDeckRequest";
import {ADD_CARD_TO_DECK_EVENT_TYPE} from "@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents";
import {DeckBuilder} from "@/src/client/DeckBuilding/domain/DeckBuilder/DeckBuilder";
import {getCatalogCardById} from "../../queries/getCatalogCardById/getCatalogCardById";
import {RootState} from "@/src/client/Shared/store/appStore/rootState";

export const addCardToDeck = createAsyncThunk<void, AddCardToDeckRequest, { state: RootState }>(
  ADD_CARD_TO_DECK_EVENT_TYPE,
  async ({cardId}, {dispatch, getState}) => {
    const state = getState();
    const catalogCard = getCatalogCardById(state, cardId);
    const deckBuilder = DeckBuilder.fromState({
      ...state.deckBuilder,
      cardsInDeck: state.deckBuilder.cardsInDraftDeck,
    });

    deckBuilder.addCard(catalogCard);
    deckBuilder.getDomainEvents().forEach(dispatch);
  }
);
