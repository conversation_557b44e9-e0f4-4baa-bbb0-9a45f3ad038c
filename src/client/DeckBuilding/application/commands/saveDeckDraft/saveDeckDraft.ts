import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from "@/src/client/Shared/store/appStore/rootState";
import {ThunkExtra} from "@/src/client/Shared/store/appStore/thunkExtra";
import {deckDraftUpdatedEvent} from "@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents";

export const saveDeckDraft = createAsyncThunk<void, void, {state: RootState; extra: ThunkExtra}>(
  'deckBuilder/saveDraft',
  async (_, {dispatch, getState, extra: {deckDraftService}}) => {
    const draft = {
      name: getState().deckBuilder.name,
      cards: Object.values(getState().deckBuilder.cardsInDraftDeck).map(({card, quantity}) => ({
        cardId: card.id,
        quantity,
      })),
    };
    deckDraftService.saveDraft(draft);
    const cards = Object.values(getState().deckBuilder.cardsInDraftDeck).map(({card, quantity}) => ({card, quantity}));
    dispatch(deckDraftUpdatedEvent({cards}));
  }
);
