import {createAsyncThunk} from '@reduxjs/toolkit';
import {RootState} from "@/src/client/Shared/store/appStore/rootState";
import {ThunkExtra} from "@/src/client/Shared/store/appStore/thunkExtra";
import {deckDraftClearedEvent} from "@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents";

export const clearDeckDraft = createAsyncThunk<void, void, {state: RootState; extra: ThunkExtra}>(
  'deckBuilder/clearDraft',
  async (_, {dispatch, extra: {deckDraftService}}) => {
    deckDraftService.clearDraft();
    dispatch(deckDraftClearedEvent());
  }
);
