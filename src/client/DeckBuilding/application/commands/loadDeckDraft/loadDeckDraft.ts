import {createAsyncThunk} from '@reduxjs/toolkit';
import {deckDraftUpdatedEvent} from '@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderEvents';
import {getCatalogCardById} from '@/src/client/DeckBuilding/application/queries/getCatalogCardById/getCatalogCardById';
import {RootState} from "@/src/client/Shared/store/appStore/rootState";
import {ThunkExtra} from "@/src/client/Shared/store/appStore/thunkExtra";

export const loadDeckDraft = createAsyncThunk<void, void, {state: RootState; extra: ThunkExtra}>(
  'deckBuilder/loadDraft',
  async (_, {dispatch, getState, extra: {deckDraftService}}) => {
    const draft = deckDraftService.loadDraft();
    if (!draft) return;
    const state = getState();
    const cards = draft.cards.map(({cardId, quantity}) => ({
      card: getCatalogCardById(state, cardId),
      quantity,
    }));
    dispatch(deckDraftUpdatedEvent({cards}));
  }
);
