import {AnyAction, Store} from '@reduxjs/toolkit';
import {initialDeckBuilderState} from '@/src/client/DeckBuilding/domain/DeckBuilder/deckBuilderReducer';
import {RootState} from "@/src/client/Shared/store/appStore/rootState";
import {AppDispatch} from "@/src/client/Shared/store/appStore/appDispatch";
import type {DeckDraftService} from '@/src/client/DeckBuilding/application/ports/DeckDraftService';

export const subscribeToDeckDraft = (
  store: Store<RootState, AnyAction> & {dispatch: AppDispatch},
  service: DeckDraftService,
) => {
  const buildDraft = (state: RootState['deckBuilder']) => ({
    name: state.name,
    cards: Object.values(state.cardsInDraftDeck).map(({card, quantity}) => ({
      cardId: card.id,
      quantity,
    })),
  });

  let previous = JSON.stringify(buildDraft(initialDeckBuilderState));
  store.subscribe(() => {
    const state = store.getState();
    const current = JSON.stringify(buildDraft(state.deckBuilder));
    if (current !== previous) {
      previous = current;
      service.saveDraft(buildDraft(state.deckBuilder));
    }
  });
};
