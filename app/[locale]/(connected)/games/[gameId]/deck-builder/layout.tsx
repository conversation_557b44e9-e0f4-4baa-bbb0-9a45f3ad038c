'use client';

import {FC, PropsWithChildren} from "react";
import {Flex, Heading, Text} from "@radix-ui/themes";
import Image from "next/image";
import {ReduxProvider} from "@/src/client/Shared/providers/ReduxProvider/ReduxProvider";
import {ConvexQueryCacheProvider} from "convex-helpers/react/cache";


const DeckBuilderLayout: FC<PropsWithChildren> = ({children}) => (
  <Flex direction="column" className="h-screen">
    <Flex align="center" justify="between" p="4" className="h-[40px] bg-[#111111] sticky top-0 z-40">
      <Flex align="center" gap="2">
        <Image src="/logos/evygames-logo.png" alt="logo" width="34" height="34"/>
        <Heading size="3">EvyGames - Deck Builder</Heading>
      </Flex>
      <Text size="2">Connected as Evyweb</Text>
    </Flex>
    <Flex direction="column" className="h-[calc(100vh-40px)]">
      <ConvexQueryCacheProvider>
        <ReduxProvider>
          {children}
        </ReduxProvider>
      </ConvexQueryCacheProvider>
    </Flex>
  </Flex>
);

export default DeckBuilderLayout;